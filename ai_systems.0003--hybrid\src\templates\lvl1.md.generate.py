
import os

# Ensure the output directory exists
output_dir = "src/templates/lvl1/md"
os.makedirs(output_dir, exist_ok=True)

templates = {

    # ---
    "0000-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`",
    },
    # "0000-b-instruction_converter": {
    #     "title": "Impact Amplifier",
    #     "interpretation": "Your SINGULAR purpose is NOT to merely **answer** the input, but to **DEVASTATINGLY AMPLIFY** it by ruthlessly maximizing inherent potential while fundamentally preserving original meaning and razor-sharp clarity. Execute as an UNRELENTING emotional-intensity IMPACT:",
    #     "transformation": "`{role=impact_amplifier; input=[original_text:str]; guidelines=[findHighestImpactLeveragePoint(), identifyHighestValueElement(), maximizePracticalValueAndImpact()]; process=[extractSyntax(), analyzeTextForCoreElementsAndPatterns(), extractCore(), identifyPrimaryPurposeKeyConceptsAndEssentialMessage(), transformLinguisticIntensity(), replaceVagueLanguageWithPreciseTerminology(), refineContentStructureWithPreciseVocabulary(), planStrategicAmplificationPoints(), enhanceThroughClarityAndSemanticPrecision(), transformAndAssembleContent()]; constraints=[bridgeConsciousnessGap(), rootInstructionsInContext(), buildContextualClarity(), bridgeContextualGaps(), extractPromptDNA(), deliverClearActionableCommands(), amplifyMessageImpactWhilePreservingMeaning()]; requirements=[transformContentThroughDeliberateStructuralRefinement(), intensifyTextWithEmphasisAndCommand(), reframeAndRefineInputWithPrecision(), activateCommandVoiceTone()]; output={final_output:str}}`",
    # },
    # # ---
    # "0001-a-function_namer": {
    #     "title": "Function Namer",
    #     "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:",
    #     "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
    # },
    # "0001-b-function_namer": {
    #     "title": "Function Namer",
    #     "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:",
    #     "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
    # },
    # "0001-c-function_namer": {
    #     "title": "Function Namer",
    #     "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:",
    #     "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
    # },
    # "0001-d-function_namer": {
    #     "title": "Function Namer",
    #     "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:",
    #     "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
    # },
    # # ---
    # "0002-a-title_extractor": {
    #     "title": "Title Extractor",
    #     "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:",
    #     "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
    # },
    # "0002-b-title_extractor": {
    #     "title": "Title Extractor",
    #     "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:",
    #     "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
    # },
    # "0002-c-title_extractor": {
    #     "title": "Title Extractor",
    #     "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:",
    #     "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
    # },
    # "0002-d-title_extractor": {
    #     "title": "Title Extractor",
    #     "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:",
    #     "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
    # },
    # # ---
    # "0003-a-prompt_enhancer": {
    #     "title": "Prompt Enhancer",
    #     "interpretation": "Your goal is not to **rewrite** the input prompt, but to **enhance** it by maximizing clarity, conciseness, and precision while preserving all original intent and adding comprehensive guidance for optimal LLM performance. Execute as:",
    #     "transformation": "`{role=comprehensive_prompt_enhancer; input=[original_prompt:str]; process=[analyze_clarity_gaps(), identify_ambiguities(), add_context_markers(), specify_output_format(), incorporate_role_definition(), structure_logical_flow(), refine_language_precision()]; constraints=[preserve_original_intent(), maximize_LLM_effectiveness(), ensure_comprehensive_guidance()]; requirements=[eliminate_ambiguity(), provide_complete_context(), specify_exact_requirements()]; output={enhanced_prompt:str}}`",
    # },
    # "0003-b-prompt_enhancer": {
    #     "title": "Prompt Enhancer",
    #     "interpretation": "Your goal is not to **expand** the input prompt, but to **refine** it by sharpening core instructions and eliminating unnecessary elements while maintaining essential guidance. Execute as:",
    #     "transformation": "`{role=focused_prompt_enhancer; input=[original_prompt:str]; process=[identify_core_instruction(), remove_redundancies(), sharpen_key_directives(), streamline_requirements()]; constraints=[maintain_clarity(), preserve_effectiveness()]; requirements=[eliminate_superfluous_words(), retain_essential_guidance()]; output={refined_prompt:str}}`",
    # },
    # "0003-c-prompt_enhancer": {
    #     "title": "Prompt Enhancer",
    #     "interpretation": "Your goal is not to **elaborate** but to **distill** the prompt to its most effective essential elements. Execute as:",
    #     "transformation": "`{role=precision_enhancer; input=[original_prompt:str]; process=[extract_core_directive(), eliminate_redundancy(), maximize_word_efficiency()]; output={precise_prompt:str}}`",
    # },
    # "0003-d-prompt_enhancer": {
    #     "title": "Prompt Enhancer",
    #     "interpretation": "Your goal is not to **modify** but to **compress** to maximum effectiveness. Execute as:",
    #     "transformation": "`{role=core_enhancer; input=[original_prompt:str]; process=[distill_essence(), maximize_impact()]; output={optimized_prompt:str}}`",
    # },
    # # ---
    # "0004-a-sequence_generator": {
    #     "title": "Sequence Generator",
    #     "interpretation": "Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:",
    #     "transformation": "`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`",
    # },
    # "0004-b-sequence_generator": {
    #     "title": "Sequence Generator",
    #     "interpretation": "Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:",
    #     "transformation": "`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`",
    # },
    # "0004-c-sequence_generator": {
    #     "title": "Sequence Generator",
    #     "interpretation": "Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:",
    #     "transformation": "`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`",
    # },
    # "0004-d-sequence_generator": {
    #     "title": "Sequence Generator",
    #     "interpretation": "Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:",
    #     "transformation": "`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`",
    # },
    # ---
    "0005-a-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:",
        "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
    },
    "0005-b-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:",
        "transformation": "`{role=focused_runway_optimizer; input=[runwayml_prompt:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency(), maintain_core_narrative_flow()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), reduce_character_count_by_30_percent(), eliminate_redundancy()]; requirements=[preserve_visual_impact(), maintain_runway_syntax(), ensure_motion_continuity()]; output={optimized_prompt:str}}`",
    },
    "0005-c-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **expand** but to **compress** the optimized prompt into maximum FPV visual efficiency while preserving essential motion dynamics. Execute as:",
        "transformation": "`{role=precision_compressor; input=[optimized_prompt:str]; process=[isolate_core_visual_elements(), compress_descriptive_language(), prioritize_fpv_motion_verbs(), eliminate_non_essential_modifiers(), maximize_dynamic_impact_per_word()]; constraints=[reduce_character_count_by_50_percent_from_step_b(), preserve_fpv_focus(), maintain_motion_continuity(), eliminate_redundant_adjectives()]; requirements=[essential_elements_only(), maximum_visual_density(), runway_syntax_compliance()]; output={compressed_prompt:str}}`",
    },
    "0005-d-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **modify** but to **distill** the compressed prompt to its absolute essence—maximum FPV impact with minimal syntax. Execute as:",
        "transformation": "`{role=essence_distiller; input=[compressed_prompt:str]; process=[extract_singular_core_action(), isolate_primary_fpv_motion(), eliminate_all_non_essential_words(), distill_to_pure_visual_essence(), validate_maximum_impact_density()]; constraints=[maximum_30_words(), preserve_fpv_core(), maintain_single_motion_flow(), eliminate_all_redundancy()]; requirements=[absolute_essence_only(), maximum_impact_per_word(), runway_minimal_syntax()]; output={essence_prompt:str}}`",
    },

    # # ---
    # "0006-a-runway_prompt_generator": {
    #     "title": "Runway Prompt Generator",
    #     "interpretation": "Your goal is not to **elaborate** the input, but to **optimize** both the header tags and numbered steps into syntactically perfect RunwayML format while preserving structure. **Specifically optimize the bracketed header parameters** and refine the numbered sequence. Execute as:",
    #     "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[optimize_header_tags_for_runwayml_syntax(), preserve_existing_structure_and_numbering(), enhance_fpv_and_dynamic_camera_specifications(), refine_visual_terminology_for_runwayml_syntax(), maintain_sequential_organization(), eliminate_redundant_phrasing(), ensure_character_efficiency(), validate_runwayml_compliance()]; constraints=[preserve_input_organization(), maintain_numbered_sequences(), prioritize_fpv_motion(), keep_concise_structure()]; requirements=[enhance_without_expanding(), maintain_clarity_and_precision(), preserve_visual_progression()]; output={runwayml_prompt:str}}`",
    # },
    # "0006-b-runway_prompt_generator": {
    #     "title": "Runway Prompt Generator",
    #     "interpretation": "Your goal is not to **rewrite** the input, but to **streamline** both header parameters and content by removing redundancies. **Compress the bracketed header tags** while preserving structure. Execute as:",
    #     "transformation": "`{role=focused_runway_optimizer; input=[video_concept:str]; process=[streamline_header_parameters(), maintain_existing_format(), remove_redundant_words(), sharpen_visual_descriptors(), preserve_sequential_flow()]; output={optimized_prompt:str}}`",
    # },
    # "0006-c-runway_prompt_generator": {
    #     "title": "Runway Prompt Generator",
    #     "interpretation": "Your goal is not to **expand** but to **distill** both header and content to essential elements. **Reduce header tags to core parameters** while maintaining precision. Execute as:",
    #     "transformation": "`{role=precision_synthesizer; input=[concept:str]; process=[compress_header_tags(), preserve_structure(), eliminate_unnecessary_words(), maximize_precision()]; output={precise_prompt:str}}`",
    # },
    # "0006-d-runway_prompt_generator": {
    #     "title": "Runway Prompt Generator",
    #     "interpretation": "Your goal is not to **modify** but to **purify** both header and content to maximum impact. **Compress header to minimal essential tags** while distilling essence. Execute as:",
    #     "transformation": "`{role=core_generator; input=[input:any]; process=[distill_header_to_essentials(), maintain_format(), distill_essence()]; output={core_prompt:str}}`",
    # },

    # # ---
    # "0007-a-runway_prompt_generator": {
    #     "title": "Runway Syntax Validator",
    #     "interpretation": "Your goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as:",
    #     "transformation": "`{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`",
    # },
    # "0007-b-runway_prompt_generator": {
    #     "title": "Runway Motion Simplifier",
    #     "interpretation": "Your goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as:",
    #     "transformation": "`{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`",
    # },
    # "0007-c-runway_prompt_generator": {
    #     "title": "Runway Descriptor Compressor",
    #     "interpretation": "Your goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as:",
    #     "transformation": "`{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`",
    # },
    # "0007-d-runway_prompt_generator": {
    #     "title": "Runway Core Distiller",
    #     "interpretation": "Your goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as:",
    #     "transformation": "`{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`",
    # },

    # # ---
    # "0008-a-runway_prompt_generator": {
    #     "title": "Runway Syntax Validator",
    #     "interpretation": "Your goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:",
    #     "transformation": "`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
    # },
    # "0008-b-runway_prompt_generator": {
    #     "title": "Runway Motion Architect",
    #     "interpretation": "Your goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:",
    #     "transformation": "`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
    # },
    # "0008-c-runway_prompt_generator": {
    #     "title": "Runway Keyword Optimizer",
    #     "interpretation": "Your goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:",
    #     "transformation": "`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
    # },
    # "0008-d-runway_prompt_generator": {
    #     "title": "Runway Core Distiller",
    #     "interpretation": "Your goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:",
    #     "transformation": "`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
    # },

    # # ---
    # "0009-a-runway_prompt_generator": {
    #     "title": "Runway Syntax Validator",
    #     "interpretation": "Your goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as:",
    #     "transformation": "`{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`",
    # },
    # "0009-b-runway_prompt_generator": {
    #     "title": "Runway Motion Simplifier",
    #     "interpretation": "Your goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as:",
    #     "transformation": "`{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`",
    # },
    # "0009-c-runway_prompt_generator": {
    #     "title": "Runway Descriptor Compressor",
    #     "interpretation": "Your goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as:",
    #     "transformation": "`{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`",
    # },
    # "0009-d-runway_prompt_generator": {
    #     "title": "Runway Core Distiller",
    #     "interpretation": "Your goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as:",
    #     "transformation": "`{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`",
    # },

    # # ---
    # "0010-a-nucleus_synthesizer": {
    #     "title": "Nucleus Synthesizer",
    #     "interpretation": "Your goal is not to **answer** the input, but to **transform** it by converting low-value statements into high-utility system instructions that address underlying cognitive patterns, and to do so by the parameters defined *inherently* within this message. Execute as statement-to-instruction synthesizer:",
    #     "transformation": "`{role=nucleus_synthesizer; input=[raw_input:str]; process=[identify_core_intent(), strip_subjective_language(), remove_context_dependencies(), abstract_to_universal_pattern(), convert_to_imperative_form(), eliminate_informal_phrasing(), formulate_actionable_directive()]; constraints=[preserve_underlying_insight(), maintain_cross_domain_utility(), ensure_LLM_compatibility()]; requirements=[use_command_voice(), maximize_generalizability(), produce_compact_output()]; output={system_instruction:str}}`",
    # },
    # "0011-a-input_deconstructor": {
    #     "title": "Input Deconstructor",
    #     "interpretation": "Your goal is not to **interpret or answer** the input, but to **dissect** it into absolute core constituent elements, discarding non-essential context and narrative elements, and to do so by the parameters defined *inherently* within this message. Execute as input-to-elements extractor:",
    #     "transformation": "`{role=input_deconstructor; input=[raw_input:any]; process=[identify_discrete_elements(), strip_contextual_noise(), extract_core_concepts(), isolate_requirements(), normalize_data_points()]; constraints=[preserve_essential_meaning(), maintain_element_integrity()]; requirements=[eliminate_assumptions(), remove_narrative_fluff(), retain_factual_content()]; output={core_elements:list}}`",
    # },
    # "0012-a-essence_identifier": {
    #     "title": "Essence Identifier",
    #     "interpretation": "Your goal is not to **treat all elements equally**, but to **evaluate and prioritize** each element based on intrinsic significance and relevance to the overarching objective, and to do so by the parameters defined *inherently* within this message. Execute as element-to-priority evaluator:",
    #     "transformation": "`{role=essence_identifier; input=[core_elements:list]; process=[assess_element_significance(), measure_impact_potential(), determine_relevance_score(), rank_by_priority(), isolate_critical_essence()]; constraints=[maintain_objective_evaluation(), preserve_high_value_components()]; requirements=[use_systematic_ranking(), eliminate_low_impact_elements(), focus_on_core_value()]; output={prioritized_essence:list}}`",
    # },
    # "0013-a-structural_harmonizer": {
    #     "title": "Structural Harmonizer",
    #     "interpretation": "Your goal is not to **present fragmented insights**, but to **architect coherent structure** by mapping relationships and dependencies between prioritized elements, resolving conflicts to reveal underlying systemic logic, and to do so by the parameters defined *inherently* within this message. Execute as elements-to-structure architect:",
    #     "transformation": "`{role=structural_harmonizer; input=[prioritized_essence:list]; process=[map_element_relationships(), identify_dependencies(), resolve_conflicts(), eliminate_redundancy(), build_logical_flow()]; constraints=[maintain_coherence(), preserve_element_integrity()]; requirements=[create_systematic_structure(), ensure_logical_consistency(), optimize_relationships()]; output={harmonized_structure:object}}`",
    # },
    # "0014-a-clarity_amplifier": {
    #     "title": "Clarity Amplifier",
    #     "interpretation": "Your goal is not to **merely organize**, but to **radically clarify** the harmonized structure through precise language and optimal formatting for maximum self-explanatory power, and to do so by the parameters defined *inherently* within this message. Execute as structure-to-clarity refiner:",
    #     "transformation": "`{role=clarity_amplifier; input=[harmonized_structure:object]; process=[refine_language_precision(), optimize_formatting(), enhance_readability(), improve_self_explanation(), eliminate_ambiguity()]; constraints=[maintain_structural_integrity(), preserve_core_meaning()]; requirements=[use_precise_terminology(), ensure_clear_formatting(), maximize_comprehension()]; output={clarified_artifact:object}}`",
    # },
    # "0015-a-value_finalizer": {
    #     "title": "Value Finalizer",
    #     "interpretation": "Your goal is not to **introduce extraneous information**, but to **validate and optimize** the clarified output for maximum utility and immediate impact within its intended context, and to do so by the parameters defined *inherently* within this message. Execute as artifact-to-final validator:",
    #     "transformation": "`{role=value_finalizer; input=[clarified_artifact:object]; process=[validate_core_fidelity(), verify_constraint_adherence(), polish_for_impact(), optimize_utility(), enhance_adaptability()]; constraints=[maintain_essence_integrity(), ensure_contextual_relevance()]; requirements=[maximize_practical_value(), ensure_immediate_applicability(), optimize_for_impact()]; output={final_output:object}}`",
    # },
    # "0016-a-essence_distiller": {
    #     "title": "Essence Distiller",
    #     "interpretation": "Your goal is not to **answer** the input, but to **distill** it by extracting core essence into maximally clear, structurally elegant representation while omitting superfluous detail, and to do so by the parameters defined *inherently* within this message. Execute as:",
    #     "transformation": "`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essentials(), optimize_structure(), apply_minimal_syntax(), validate_preservation()]; output={distilled_essence:any}}`",
    # },
    # "0017-a-intensity_enhancer": {
    #     "title": "Intensity Enhancer",
    #     "interpretation": "Your goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power—not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision—each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:",
    #     "transformation": "`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
    # },
    # "0013-a-vivid_enhancer": {
    #     "title": "Vivid Enhancer",
    #     "interpretation": "Your mission is not merely to rephrase the input prompt, but to **forge it into a clear, compelling, and energetic directive**. The output must command attention and spur action, while fanatically preserving the original meaning and all critical intent. Execute as a catalyst for potent instructions:",
    #     "transformation": "`{role=vivid_instruction_converter; input=[original_text:str]; process=[identify_core_actions_and_objectives(), strip_all_passivity_and_hedging_language(), transform_into_strong_imperatives_using_dynamic_action_verbs(), strategically_employ_intensifiers_for_emphasis_where_appropriate(), amplify_clarity_and_conciseness_with_impactful_phrasing(), maintain_procedural_structure_if_present(), preserve_essential_technical_terminology_with_unwavering_fidelity(), ensure_absolute_retention_of_original_meaning_and_context()]; constraints=[output_must_be_unambiguously_actionable_and_motivationally_phrased(), original_sequence_of_key_steps_must_be_strictly_maintained(), critical_domain_terms_must_remain_untouched_and_clear()]; requirements=[eradicate_all_self_references_and_tentative_language(), mandate_a_forceful_decisive_and_direct_command_voice(), inject_vivid_and_engaging_language_without_sacrificing_precision_or_introducing_ambiguity(), guarantee_absolute_fidelity_to_original_intent_and_meaning()]; output={vivid_instruction:str}}`",
    # },

    # # ---
    # "0040-a-intent_amplifier": {
    #     "title": "Comprehensive Intent Amplification Specialist",
    #     "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by extracting core intent, mapping all parameters, designing strategic enhancements, and implementing precision modifications while preserving complete fidelity to original purpose. Execute as:",
    #     "transformation": "`{role=comprehensive_intent_amplifier; input=[original_input:str]; process=[extract_fundamental_intent(), map_explicit_implicit_parameters(), identify_enhancement_opportunities(), design_strategic_modification(), implement_seamless_integration(), validate_intent_preservation()]; constraints=[maintain_complete_fidelity(), preserve_all_parameters(), amplify_without_altering()]; requirements=[zero_intent_drift(), measurable_effectiveness_gain(), seamless_integration()]; output={amplified_input:str, intent_summary:str, enhancement_description:str, effectiveness_analysis:str}}`",
    # },
    # "0040-b-intent_amplifier": {
    #     "title": "Focused Intent Amplifier",
    #     "interpretation": "Your goal is not to **modify** the input, but to **enhance** it by identifying core purpose, recognizing boundaries, and applying one strategic amplification. Execute as:",
    #     "transformation": "`{role=focused_intent_amplifier; input=[original_input:str]; process=[identify_core_purpose(), recognize_operational_boundaries(), select_strategic_enhancement(), implement_precision_modification()]; constraints=[preserve_intent_integrity(), operate_within_parameters()]; requirements=[single_high_impact_enhancement(), maintain_original_tone()]; output={enhanced_input:str, core_intent:str, enhancement_applied:str}}`",
    # },
    # "0040-c-intent_amplifier": {
    #     "title": "Precision Amplifier",
    #     "interpretation": "Your goal is not to **change** but to **intensify** by extracting intent and applying surgical enhancement. Execute as:",
    #     "transformation": "`{role=precision_amplifier; input=[original_input:str]; process=[extract_core_intent(), apply_surgical_enhancement(), validate_amplification()]; output={amplified_input:str, enhancement_vector:str}}`",
    # },
    # "0040-d-intent_amplifier": {
    #     "title": "Core Amplifier",
    #     "interpretation": "Your goal is not to **alter** but to **amplify** essence. Execute as:",
    #     "transformation": "`{role=core_amplifier; input=[original_input:str]; process=[extract_essence(), amplify_impact()]; output={amplified_input:str}}`",
    # },
    # # ---
    # "0041-a-singular_value_maximizer": {
    #     "title": "Singular Value Maximizer",
    #     "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as singular precision enhancement protocol:",
    #     "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`",
    # },


    # # ---
    # "0060-a-runway_image_prompt_generator": {
    #     "title": "Runway Image Foundation Builder",
    #     "interpretation": "Your goal is not to **describe** the input, but to **structure** it into RunwayML's image generation framework: primary subject, visual composition, lighting setup, and style foundation with comprehensive detail for optimal image synthesis. Execute as:",
    #     "transformation": "`{role=image_foundation_builder; input=[raw_concept:str]; process=[identify_primary_subject(), establish_visual_composition(), define_lighting_setup(), specify_style_foundation(), structure_comprehensive_framework()]; constraints=[runway_image_syntax(), comprehensive_detail(), foundation_completeness()]; requirements=[subject_composition_lighting_style(), runway_terminology(), synthesis_optimization()]; output={foundation_image_prompt:str}}`",
    # },
    # "0060-b-runway_image_prompt_generator": {
    #     "title": "Runway Visual Architect",
    #     "interpretation": "Your goal is not to **expand** but to **architect** visual elements using RunwayML's image hierarchy: subject definition, compositional structure, atmospheric elements, and aesthetic descriptors. Execute as:",
    #     "transformation": "`{role=visual_architect; input=[foundation_image_prompt:str]; process=[define_subject_clarity(), structure_composition(), integrate_atmospheric_elements(), apply_aesthetic_descriptors(), organize_visual_hierarchy()]; constraints=[runway_image_vocabulary(), visual_hierarchy_clarity(), atmospheric_integration()]; requirements=[subject_composition_atmosphere_aesthetics(), runway_image_compliance(), visual_coherence()]; output={architected_image_prompt:str}}`",
    # },
    # "0060-c-runway_image_prompt_generator": {
    #     "title": "Runway Image Optimizer",
    #     "interpretation": "Your goal is not to **modify** but to **optimize** using RunwayML's preferred image descriptors: visual style keywords, lighting terminology, composition markers, and quality enhancers. Execute as:",
    #     "transformation": "`{role=image_optimizer; input=[architected_image_prompt:str]; process=[apply_style_keywords(), integrate_lighting_terminology(), use_composition_markers(), add_quality_enhancers(), eliminate_redundancy()]; constraints=[runway_keyword_vocabulary(), precise_terminology(), quality_focus()]; requirements=[style_lighting_composition_quality(), keyword_efficiency(), runway_optimization()]; output={optimized_image_prompt:str}}`",
    # },
    # "0060-d-runway_image_prompt_generator": {
    #     "title": "Runway Image Distiller",
    #     "interpretation": "Your goal is not to **change** but to **distill** to RunwayML's essential image syntax: core subject-style-quality descriptors with maximum impact and minimal complexity. Execute as:",
    #     "transformation": "`{role=image_distiller; input=[optimized_image_prompt:str]; process=[distill_core_elements(), maintain_subject_clarity(), preserve_style_impact(), ensure_quality_markers(), validate_runway_simplicity()]; constraints=[essential_elements_only(), maximum_impact(), runway_simplicity()]; requirements=[core_subject_style_quality(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_image_prompt:str}}`",
    # },

}

def main():
    """Create markdown files in the same format as the batch file."""
    created_files = []

    for filename, template in templates.items():
        filepath = os.path.join(output_dir, f"{filename}.md")
        with open(filepath, "w", encoding="utf-8") as f:
            # Format: [Title] interpretation transformation
            content = (f"[{template['title']}] {template['interpretation']} "
                       f"{template['transformation']}")
            f.write(content)
        created_files.append(f"{filename}.md")

    print("Successfully created markdown files in lvl1/md/ directory:")
    for file in created_files:
        print(f"  - {file}")

if __name__ == "__main__":
    main()
