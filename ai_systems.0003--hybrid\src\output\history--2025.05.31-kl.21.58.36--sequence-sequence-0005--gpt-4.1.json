  {
  "initial_prompt": "GOAL: AMPLIFY THE VISUAL PROMPT SO THE EACH ELEMENT *TRANSFORMS* INTO THE NEXT\n\nGolden lighting cascades across transformation points, they *transform* and *morphs* into flowing lines of elegant golden metallic shimmer.  Award-winning cinematography with pristine 8K details that document each millisecond of the complete metamorphosis process without digital shortcuts",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "GOAL: AMPLIFY THE VISUAL PROMPT SO THE EACH ELEMENT *TRANSFORMS* INTO THE NEXT\n\nGolden lighting cascades across transformation points, they *transform* and *morphs* into flowing lines of elegant golden metallic shimmer.  Award-winning cinematography with pristine 8K details that document each millisecond of the complete metamorphosis process without digital shortcuts",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"An FPV camera glides smoothly through a surreal landscape where golden lighting dynamically cascades across multiple transformation points. As the camera seamlessly orbits and weaves between these points, each element transforms and morphs fluidly, dissolving into flowing lines of elegant golden metallic shimmer. Continuous dynamic CG movements capture every millisecond of the complete metamorphosis in pristine 8K detail, with award-winning cinematic lighting sweeping across each transformation. The camera moves intimately close, capturing shimmering reflections and intricate details, creating an immersive, hypnotic sequence where each form elegantly shifts and transforms into the next without interruption.\"\n}",
        }
      }
    }