[Runway Prompt Generator] Your goal is not to **expand** but to **compress** the optimized prompt into maximum FPV visual efficiency while preserving essential motion dynamics. Execute as: `{role=precision_compressor; input=[optimized_prompt:str]; process=[isolate_core_visual_elements(), compress_descriptive_language(), prioritize_fpv_motion_verbs(), eliminate_non_essential_modifiers(), maximize_dynamic_impact_per_word()]; constraints=[reduce_character_count_by_50_percent_from_step_b(), preserve_fpv_focus(), maintain_motion_continuity(), eliminate_redundant_adjectives()]; requirements=[essential_elements_only(), maximum_visual_density(), runway_syntax_compliance()]; output={compressed_prompt:str}}`
