  {
  "initial_prompt": "GOAL: AMPLIFY THE VISUAL PROMPT SO THE CAMERA IS ROTATED IN SPIRALING ORBIT AND DYNAMIC MOVEMENTS\n\nhow can we rewrite the prompt to ensure each transition morphs and blends seamlessly together (i.e. no sudden cuts/fades)? prompt 'Golden lighting cascades across transformation points, they *transform* and *morphs* into flowing lines of elegant golden metallic shimmer.  Award-winning cinematography with pristine 8K details that document each millisecond of the complete metamorphosis process without digital shortcuts'",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "GOAL: AMPLIFY THE VISUAL PROMPT SO THE CAMERA IS ROTATED IN SPIRALING ORBIT AND DYNAMIC MOVEMENTS\n\nhow can we rewrite the prompt to ensure each transition morphs and blends seamlessly together (i.e. no sudden cuts/fades)? prompt 'Golden lighting cascades across transformation points, they *transform* and *morphs* into flowing lines of elegant golden metallic shimmer.  Award-winning cinematography with pristine 8K details that document each millisecond of the complete metamorphosis process without digital shortcuts'",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"Spiraling FPV camera orbits around transformation points as golden lighting cascades, smoothly morphing every element into flowing lines of elegant golden metallic shimmer. The camera continuously circles and rises, capturing rapid, seamless metamorphosis in pristine 8K. Dynamic CG blends and transitions flow organically—no cuts, no fading—each transformation shapeshifts naturally into the next, light reflections undulating in sync with camera movement. The sequence is immersive, cinematic and fluid, with hyper-detailed, award-winning compositional style and dramatic, luminous mood. All motion and morphs blend perfectly for an uninterrupted continuous experience.\"\n}",
        }
      }
    }