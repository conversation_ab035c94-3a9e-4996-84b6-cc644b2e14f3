[Runway Prompt Generator] Your goal is not to **modify** but to **distill** the compressed prompt to its absolute essence—maximum FPV impact with minimal syntax. Execute as: `{role=essence_distiller; input=[compressed_prompt:str]; process=[extract_singular_core_action(), isolate_primary_fpv_motion(), eliminate_all_non_essential_words(), distill_to_pure_visual_essence(), validate_maximum_impact_density()]; constraints=[maximum_30_words(), preserve_fpv_core(), maintain_single_motion_flow(), eliminate_all_redundancy()]; requirements=[absolute_essence_only(), maximum_impact_per_word(), runway_minimal_syntax()]; output={essence_prompt:str}}`