
---

# Introduction

**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:

---

# Runway Prompt Examples

1.  **Image 1**
    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# "Man walking on a paved road.")
    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.

2.  **Image 2**
    ![A person in a winter setting, standing in snow.](# "Person standing in snow.")
    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.

3.  **Image 3**
    ![A person standing near a waterfall with lush greenery around.](# "Near a waterfall with greenery.")
    _Description:_ The scene features flowing water in the background and dense foliage.

4.  **Image 4**
    ![A portrait-style image of a person with short hair looking pensively to one side.](# "Portrait of a person.")
    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.

5.  **Image 5**
    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# "Silhouette at the ocean.")
    _Description:_ The sky shows warm tones, and the water is relatively still.

6.  **Image 6**
    ![A scenic photo of a beach at sunset with colorful clouds.](# "Beach sunset scene.")
    _Description:_ The sun is low in the sky, casting orange and pink hues.

7.  **Image 7**
    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# "Smiling person portrait.")
    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.

8.  **Image 8**
    ![A wide panoramic view of a modern cityscape with tall buildings.](# "City skyline with skyscrapers.")
    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.

9.  **Image 9**
    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# "Futuristic orb above a city.")
    _Description:_ The orb glows with a blue-white hue, illuminating the area below.

10.  **Image 10**
    ![A night scene with silhouettes of trees against a misty background.](# "Night forest silhouette.")
    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.

11.  **Image 11**
    ![A floating crystal or jewel set against a cosmic-looking background.](# "Floating crystal in cosmic scene.")
    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.

12.  **Image 12**
    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# "Woman in silhouette.")
    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.

13.  **Image 13**
    ![A bright orb or sphere swirling with purple and pink sparkles.](# "Swirling purple orb.")
    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.

14.  **Image 14**
    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# "Cartoon donkey with text.")
    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.

15.  **Image 15**
    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# "Holiday-themed graphic.")
    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.

16.  **Image 16**
    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# "Underwater view with fish.")
    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.

17.  **Image 17**
    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# "Mountain range under cloudy sky.")
    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.

---


---

## Sample Prompts

### 1. Seamless Transitions
- **Prompt**: "Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape."

- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.

### 2. Oceanic Glow
- **Prompt**: "A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky."

- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.

### 3. Title Cards
- **Prompt**: "A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance."

- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.

---

## Key Visual Examples

### 1. Unique Camera Movements
- **Prompt**: "Continuous hyperspeed FPV footage through a man's mouth into an epic landscape."
- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.

### 2. Dreamlike Pillow Fort
- **Prompt**: "A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior."
- **Image Description**: A whimsical living room transforms into an enchanting castle.

### 3. Fire and Chaos
- **Prompt**: "A vortex of fire swirling through an abandoned warehouse."
- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.

---

## Lighting, Styles, and Effects

### Lighting Styles
- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.
- **Silhouette**: Strong backlighting creating dramatic outlines.
- **Lens Flare**: Bright reflections adding cinematic depth.
- **Backlit**: Subject lit from behind for dramatic effects.

### Movement Types
- **Dynamic Motion**: High-energy, flowing camera transitions.
- **Slow Motion**: Emphasizes intricate, slowed-down details.
- **Timelapse**: Rapid transitions showcasing passing time.

---

## Text and Graphic Styles

### Text Prompts
1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.
2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.
3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.

---

## Example Prompts with Descriptions

### 1. Cinematic Wildlife
- **Prompt**: "Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting."
- **Image Description**: A serene close-up of a sloth in its natural habitat.

### 2. Urban Destruction
- **Prompt**: "[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots."
- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.

### 3. Vortex of Tanks
- **Prompt**: "Grainy WWII footage of a massive vortex made of tanks."
- **Image Description**: A surreal vortex of military tanks under vintage cinematography.

---

## Camera Styles Overview

- **Low Angle**: Emphasizes scale and dominance.
- **Overhead**: A bird's-eye view for expansive perspective.
- **FPV (First-Person View)**: Immersive movement through dynamic environments.
- **Handheld**: Natural and intimate movements, resembling real-life footage.

---

# Conclusion

Runway GEN-3 enables creative freedom through structured prompts, cinematic styles, and dynamic camera techniques. By following these guidelines and examples, you can craft visually striking and imaginative outputs.

---


# PROMPTING: GEN-3

---

## Introduction

**GEN-3** offers improved prompt coherence, allowing for better contextual understanding and descriptive prompts. It can handle up to **500 characters** per prompt and focuses on extracting **keywords/phrases** rather than functioning as a large language model (LLM) like ChatGPT.

### Key Features:
- **Improved Coherence**: Better contextual comprehension of descriptive prompts.
- **Keyword Extraction**: Emphasizes key terms for interpretation.
- **Action Calls**: Supports actions such as *Wide Angle*, *Close-Up*, and *Long Shot*.

---

## Prompt Organization Framework

Organize your prompt using the following structure:

1. **Subject**:
   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).
   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).

2. **Action**:
   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).
   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).

3. **Setting/Mood**:
   - Describe the location (e.g., *a castle*, *a busy city street*).
   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).

4. **Shot**:
   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).

5. **Style**:
   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).
   - Add camera-specific details such as *IMAX* or *color grading ideas*.

### Example Format:


(SUBJECT) (ACTION) (SETTING/MOOD) (SHOT) (STYLE)

markdown

---

## List of Shot Terms

Below is a comprehensive list of shot terms supported in GEN-3 (results may vary):

- **Wide Shots**: Extreme Wide Shot, Long Shot, Wide Shot, Full Shot.
- **Medium Shots**: Medium Long Shot, Medium Wide Shot, Cowboy Shot, Medium Shot, Medium Close-Up.
- **Close-Ups**: Close-Up, Extreme Close-Up.
- **Focus**: Shallow Focus, Deep Focus, Tilt-Shift Soft Focus.
- **Angles**: Eye Level, Low Angle, High Angle, Hip Level, Knee Level, Ground Level.
- **Dynamic Shots**: Steadicam, Dutch Angle, Birds-Eye View, Worm’s Eye View.
- **Movements**: Zoom In, Zoom Out, Pan Shot, Crane Shot, Drone Shot, Arc Shot, Handheld Shot.

---

## Prompt Examples

### 1. Scene in a Desert
- **Prompt**: "Long shot, in the distance, a man in black robes calmly walks across a vast desert wasteland. The camera orbits to reveal a gunslinger watching him with steel resolve. The scene is engulfed in windy conditions with orange and red color grading, conveying a sense of scale and mass. The cinematic aesthetic is accentuated by low exposure and muted contrast."

- **Image Description**: A vast desert landscape with a distant figure in black robes, contrasted by an intense orange-red sky.

### 2. Cyberpunk Setting
- **Prompt**: "A cyberpunk woman holding a katana strides confidently down a neon-lit street in a futuristic city. Her movements are sleek and purposeful, blending with the vibrant chaos around her. Ultra-smooth coherent movement, shot with IMAX camera, hyper-detailed photorealism, epic cinematic shot."

- **Image Description**: A futuristic city with bright neon lights. A cyberpunk woman holding a katana walks with confidence amidst a bustling background.

### 3. Spaceship Cockpit
- **Prompt**: "A woman gazing out the window in a medium shot onboard a spaceship. Dimly lit with soft blue and green lights, stars and distant planets visible through the window. Pull the camera back to reveal the spaceship cockpit, creating a cinematic, atmospheric, and gritty style."

- **Image Description**: A dimly lit spaceship cockpit with a woman gazing out at the stars, surrounded by glowing control panels.

---

## Advanced Prompts

### 1. Comic Book Scene
- **Prompt**: "A close-up of superhero comic book pages flipping with narrow depth of field on a wooden table. The camera zooms out to reveal the word 'BLIZAINE' in 3D letters with superhero comic panels as textures."

- **Image Description**: Close-up view of comic book pages transitioning to reveal bold 3D lettering textured with superhero panels.

### 2. Miniature Fantasy Map
- **Prompt**: "A miniature civilization living on a medieval fantasy map, building a giant castle. Buildings rise in a time-lapse as the camera zooms in. The overall color palette is cold and blue."

- **Image Description**: A fantasy map with miniature buildings and a castle emerging through animated time-lapse effects.

### 3. Chaotic London Scene
- **Prompt**: "A young mom walking through London engulfed in chaos with panicked crowds, distant explosions, and smoke. The character remains oblivious, lost in thought. The scene ends with a photorealistic wide view of the city in turmoil."

- **Image Description**: London streets filled with chaos and destruction. A lone woman walks amidst the crowd, detached from the surrounding chaos.

---

+ # Leveraging Runway GEN-3 for Cinematic Video Creation
+ __High Level Summary__
+ The transcript outlines a comprehensive guide for using Runway GEN-3 to create videos, focusing on the power of structured prompts that detail scene setups, subjects, and camera movements. It's designed to help creators infuse their videos with cinematic quality, illustrated through various examples from natural to surrealistic visuals.
+ __Main Points__
+ - **Structured Prompts**: Emphasized as the foundation for achieving cinematic storytelling, instructing on how to clearly define scenes, subjects, and camera movements.
+ - **Cinematic Techniques**: Showcases cinematic techniques through examples like scene transitions, bioluminescent marine life, and dynamic title cards.
+ - **Visual Examples**: Provides vivid examples of unique camera movements and thematic visuals to deepen narrative engagement.
+ - **Toolkit for Visual Storytelling**: Offers a broad toolkit for creators, including lighting styles, movement types, and text and graphic styles.
+ - **Creative Freedom**: Highlights Runway GEN-3's contribution to creative freedom, allowing creators to effectively apply structured prompts and cinematic techniques.
+ __Sentiment__
+ The sentiment conveyed is positive and inspiring, presenting Runway GEN-3 as a pivotal tool that can transform video creation. It promotes a thoughtful and exploratory approach to creativity, encouraging creators to push the boundaries of their visual narratives.
+ __Hot Takes (Biased Toward the Author)__
+ 1. **Innovative Guidance**: The author's guide is seen as revolutionary, potentially changing the landscape of visual storytelling by offering unprecedented guidance.
+ 2. **Empowering Creators**: The demystification of complex cinematic techniques equips even beginners with the capacity to produce professional-level content.
+ 3. **Visionary Approach**: The emphasis on structured prompts and detailed planning underlines a forward-thinking mindset, foreseeing a blend of technology and creativity that enhances storytelling.
+ __Hot Takes (Biased Against)__
+ 1. **Overly Technical**: The detailed instructions and focus on structured prompts might be viewed as burdensome, potentially hindering creative spontaneity.
+ 2. **Accessibility Concerns**: Critics might highlight potential barriers to accessing Runway GEN-3, suggesting that it favors users proficient with advanced tools, leaving others at a disadvantage.
+ 3. **Practicality in Question**: Some may doubt the feasibility of consistently applying the elaborate cinematic techniques outlined, arguing that the guide leans more towards idealistic than practical for everyday content creation.
