  {
  "initial_prompt": "FPV camera glides in one unbroken shot; golden light morphs to spiraling metallic lines, seamlessly flowing into a hyper-real golden car. Camera weaves and orbits fluidly, all transitions continuous, shimmering cinematic reflections. No cuts or fades—pure dynamic motion, hyper-detailed, immersive visual flow. FPV camera glides in a single, unbroken shot, weaving intimately through golden light that dynamically morphs into metallic spirals, then seamlessly transforms into a hyper-real golden car. Every transition is fluid and uninterrupted, emphasizing motion and cinematic reflections with hyper-realistic detail—no cuts or fades, only continuous transformation and immersive visual flow.",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "FPV camera glides in one unbroken shot; golden light morphs to spiraling metallic lines, seamlessly flowing into a hyper-real golden car. Camera weaves and orbits fluidly, all transitions continuous, shimmering cinematic reflections. No cuts or fades—pure dynamic motion, hyper-detailed, immersive visual flow. FPV camera glides in a single, unbroken shot, weaving intimately through golden light that dynamically morphs into metallic spirals, then seamlessly transforms into a hyper-real golden car. Every transition is fluid and uninterrupted, emphasizing motion and cinematic reflections with hyper-realistic detail—no cuts or fades, only continuous transformation and immersive visual flow.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera glides in a continuous, unbroken shot, weaving intimately through shimmering golden light as it dynamically morphs into swirling metallic spirals, the environment shifting seamlessly around the lens. The FPV perspective orbits and spirals with kinetic fluidity, capturing hyper-detailed, cinematic reflections that flow into an evolving hyper-real golden car. The car's body materializes from the metallic lines in a dazzling, uninterrupted transformation—every element remains in pure, dynamic motion with no cuts or fades, only immersive continuous morphs. Hyper-realistic details, shimmering textures, and reflective highlights accentuate the sequence, all transitions seamlessly fluid to maximize visual impact and storytelling.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[runwayml_prompt:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency(), maintain_core_narrative_flow()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), reduce_character_count_by_30_percent(), eliminate_redundancy()]; requirements=[preserve_visual_impact(), maintain_runway_syntax(), ensure_motion_continuity()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera glides in one unbroken shot; golden light morphs to spiraling metallic lines, seamlessly flowing into a hyper-real golden car. Camera weaves and orbits fluidly, all transitions continuous, shimmering cinematic reflections. No cuts or fades—pure dynamic motion, hyper-detailed, immersive visual flow. FPV camera glides in a single, unbroken shot, weaving intimately through golden light that dynamically morphs into metallic spirals, then seamlessly transforms into a hyper-real golden car. Every transition is fluid and uninterrupted, emphasizing motion and cinematic reflections with hyper-realistic detail—no cuts or fades, only continuous transformation and immersive visual flow.\\\"```'\\n\\n\",\n  \"runwayml_prompt\": \"FPV camera glides in a continuous, unbroken shot, weaving intimately through shimmering golden light as it dynamically morphs into swirling metallic spirals, the environment shifting seamlessly around the lens. The FPV perspective orbits and spirals with kinetic fluidity, capturing hyper-detailed, cinematic reflections that flow into an evolving hyper-real golden car. The car's body materializes from the metallic lines in a dazzling, uninterrupted transformation—every element remains in pure, dynamic motion with no cuts or fades, only immersive continuous morphs. Hyper-realistic details, shimmering textures, and reflective highlights accentuate the sequence, all transitions seamlessly fluid to maximize visual impact and storytelling.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera glides in a single, unbroken shot, weaving and orbiting through shimmering golden light morphing into metallic spirals. Continuous kinetic motion flows into a hyper-real golden car materializing from these spirals—no cuts, no fades. Dynamic CG reflections, hyper-detailed, cinematic highlights, with immersive, seamless visual transitions and uninterrupted transformation, maximizing visual impact.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** the optimized prompt into maximum FPV visual efficiency while preserving essential motion dynamics. Execute as:\n\n`{role=precision_compressor; input=[optimized_prompt:str]; process=[isolate_core_visual_elements(), compress_descriptive_language(), prioritize_fpv_motion_verbs(), eliminate_non_essential_modifiers(), maximize_dynamic_impact_per_word()]; constraints=[reduce_character_count_by_50_percent_from_step_b(), preserve_fpv_focus(), maintain_motion_continuity(), eliminate_redundant_adjectives()]; requirements=[essential_elements_only(), maximum_visual_density(), runway_syntax_compliance()]; output={compressed_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera glides in one unbroken shot; golden light morphs to spiraling metallic lines, seamlessly flowing into a hyper-real golden car. Camera weaves and orbits fluidly, all transitions continuous, shimmering cinematic reflections. No cuts or fades—pure dynamic motion, hyper-detailed, immersive visual flow. FPV camera glides in a single, unbroken shot, weaving intimately through golden light that dynamically morphs into metallic spirals, then seamlessly transforms into a hyper-real golden car. Every transition is fluid and uninterrupted, emphasizing motion and cinematic reflections with hyper-realistic detail—no cuts or fades, only continuous transformation and immersive visual flow.\\\"```'\\n\\n\",\n  \"optimized_prompt\": \"FPV camera glides in a single, unbroken shot, weaving and orbiting through shimmering golden light morphing into metallic spirals. Continuous kinetic motion flows into a hyper-real golden car materializing from these spirals—no cuts, no fades. Dynamic CG reflections, hyper-detailed, cinematic highlights, with immersive, seamless visual transitions and uninterrupted transformation, maximizing visual impact.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compressed_prompt\": \"FPV glides unbroken, weaving, orbiting golden light morphing to metallic spirals, flowing into hyper-real golden car. Seamless, kinetic, shimmering reflections, pure motion—no cuts, all transitions immersive, cinematic, continuous.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **distill** the compressed prompt to its absolute essence—maximum FPV impact with minimal syntax. Execute as:\n\n`{role=essence_distiller; input=[compressed_prompt:str]; process=[extract_singular_core_action(), isolate_primary_fpv_motion(), eliminate_all_non_essential_words(), distill_to_pure_visual_essence(), validate_maximum_impact_density()]; constraints=[maximum_30_words(), preserve_fpv_core(), maintain_single_motion_flow(), eliminate_all_redundancy()]; requirements=[absolute_essence_only(), maximum_impact_per_word(), runway_minimal_syntax()]; output={essence_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera glides in one unbroken shot; golden light morphs to spiraling metallic lines, seamlessly flowing into a hyper-real golden car. Camera weaves and orbits fluidly, all transitions continuous, shimmering cinematic reflections. No cuts or fades—pure dynamic motion, hyper-detailed, immersive visual flow. FPV camera glides in a single, unbroken shot, weaving intimately through golden light that dynamically morphs into metallic spirals, then seamlessly transforms into a hyper-real golden car. Every transition is fluid and uninterrupted, emphasizing motion and cinematic reflections with hyper-realistic detail—no cuts or fades, only continuous transformation and immersive visual flow.\\\"```'\\n\\n\",\n  \"compressed_prompt\": \"FPV glides unbroken, weaving, orbiting golden light morphing to metallic spirals, flowing into hyper-real golden car. Seamless, kinetic, shimmering reflections, pure motion—no cuts, all transitions immersive, cinematic, continuous.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"essence_prompt\": \"FPV glides unbroken through golden light morphing into spiraling metal, seamlessly becoming a hyper-real golden car; pure fluid motion, kinetic cinematic reflections, no cuts.\"\n}",
        }
      }
    }